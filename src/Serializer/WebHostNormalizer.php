<?php

namespace App\Serializer;

use App\Entity\WebHost\WebHost;
use App\Entity\WebHost\WebHostConfiguration\DockerSwarmConfiguration;
use App\Entity\WebHost\WebHostConfiguration\VirtualMachineConfiguration;
use App\Service\Favicon\FaviconRetriever;
use App\Service\SwarmpitAPI;
use Symfony\Component\Serializer\Normalizer\NormalizerInterface;

class WebHostNormalizer implements NormalizerInterface
{
    public function __construct(
        private readonly FaviconRetriever $faviconRetriever,
        private readonly SwarmpitAPI $swarmpitAPI,
        private ?array $services = null,
    ) {
    }

    /**
     * @param WebHost $data
     */
    public function normalize(mixed $data, ?string $format = null, array $context = []): array
    {
        if (isset($context['groups']) && in_array('chrome-extension', $context['groups'], true)) {
            return $this->toArrayChromeExtension($data);
        }

        return $this->toArray($data);
    }

    public function supportsNormalization(mixed $data, ?string $format = null, array $context = []): bool
    {
        return $data instanceof WebHost;
    }

    /**
     * @return array<class-string, bool>
     */
    public function getSupportedTypes(?string $format): array
    {
        return [WebHost::class => true];
    }

    private function toArray(WebHost $webHost): array
    {
        $urls = [];
        foreach ($webHost->getUrls() as $url) {
            $urls[] = [
                'id' => $url->getId(),
                'url' => $url->getUrl(),
                'healthCheckReport' => [
                    'status' => $url->getHealthCheckReport()?->status?->value,
                ],
                'sslCertificateReport' => [
                    'isValid' => $url->getSslCertificateReport()?->isValid,
                ],
            ];
        }

        $associatedUrls = array_map(function (string $url) {
            return [
                'url' => $url,
                'favicon' => $this->faviconRetriever->__invoke($url),
            ];
        }, $webHost->getAssociatedUrls());

        $configuration = $webHost->getConfiguration();
        $stack = $configuration instanceof DockerSwarmConfiguration ? $configuration->getStack() : null;

        return [
            'id' => $webHost->getId(),
            'name' => $webHost->getName(),
            'environnement' => $webHost->getEnvironnement()?->value,
            'expectedVisibility' => $webHost->getExpectedVisibility()?->value,
            'gitlabRemoteUrl' => $webHost->getGitlabRemoteUrl(),
            'gitlabActiveBranch' => $webHost->getGitlabActiveBranch(),
            'lastCommitDate' => $webHost->getLastCommitDate(),
            'configuration' => [
                'type' => $configuration?->getType(),
                'stack' => $stack,
                'webId' => $configuration?->getWebId(),
            ],
            'urls' => $urls,
            'confluenceUrl' => $webHost->getConfluenceUrl(),
            'databaseUrl' => $webHost->getDatabaseExplorerUrl(),
            'associatedUrls' => $associatedUrls,
        ];
    }

    private function toArrayChromeExtension(WebHost $webHost): array
    {
        $urls = [];
        foreach ($webHost->getUrls() as $url) {
            $urls[] = [
                'id' => $url->getId(),
                'url' => $url->getUrl(),
                'healthCheckReport' => [
                    'status' => $url->getHealthCheckReport()?->status?->value,
                ],
                'sslCertificateReport' => [
                    'isValid' => $url->getSslCertificateReport()?->isValid,
                ],
            ];
        }

        $associatedUrls = array_map(function (string $url) {
            return [
                'url' => $url,
                'favicon' => $this->faviconRetriever->__invoke($url),
            ];
        }, $webHost->getAssociatedUrls());

        $configuration = $webHost->getConfiguration();
        $server = $configuration instanceof DockerSwarmConfiguration
            ? $configuration->getClusterName()?->value
            : ($configuration instanceof VirtualMachineConfiguration ? $configuration->getIp() : null);
        $stack = $configuration instanceof DockerSwarmConfiguration ? $configuration->getStack() : null;

        $service = null;
        if ($configuration instanceof DockerSwarmConfiguration && $configuration->getWebId()) {
            $service = $this->findServiceInSwarmpit($configuration);
        }

        return [
            'id' => $webHost->getId(),
            'name' => $webHost->getName(),
            'environnement' => $webHost->getEnvironnement()?->value,
            'expectedVisibility' => $webHost->getExpectedVisibility()?->value,
            'gitlabRemoteUrl' => $webHost->getGitlabRemoteUrl(),
            'gitlabActiveBranch' => $webHost->getGitlabActiveBranch(),
            'lastCommitDate' => $webHost->getLastCommitDate(),
            'configuration' => [
                'server' => $server,
                'type' => $configuration?->getType(),
                'stack' => $stack,
                'webId' => $configuration?->getWebId(),
            ],
            'urls' => $urls,
            'confluenceUrl' => $webHost->getConfluenceUrl(),
            'databaseUrl' => $webHost->getDatabaseExplorerUrl(),
            'associatedUrls' => $associatedUrls,
            'service' => $service, // todo formatter correctement
        ];
    }

    public function findServiceInSwarmpit(DockerSwarmConfiguration $configuration): ?array
    {
        try {
            $services = $this->getServices()['services'];
        } catch (\Exception $exception) {
            return null;
        }

        return array_find($services, fn ($service) => $service['stack'] === $configuration->getWebId() && 'front_web' === $service['name']);
    }

    public function getServices()
    {
        if (null === $this->services) {
            $this->services = $this->swarmpitAPI->getAllServices();
        }

        return $this->services;
    }
}
